# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: translation-testupstream
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: translation-testupstream
  namespace: default
spec:
  gatewayClassName: translation-testupstream
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: translation-testupstream
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: translation-testupstream
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: some-cool-model
      backendRefs:
        - name: translation-testupstream-cool-model-backend
          weight: 100
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: another-cool-model
      backendRefs:
        - name: translation-testupstream-another-cool-model-backend
          weight: 100
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: translation-testupstream-cool-model-backend
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: testupstream
    kind: Backend
    group: gateway.envoyproxy.io
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: translation-testupstream-another-cool-model-backend
  namespace: default
spec:
  schema:
    name: AWSBedrock
  backendRef:
    name: testupstream-canary
    kind: Backend
    group: gateway.envoyproxy.io
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: testupstream
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: testupstream.default.svc.cluster.local
        port: 80
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: testupstream-canary
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: testupstream-canary.default.svc.cluster.local
        port: 80
