// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

//go:build test_e2e

package e2e

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestInferencePoolIntegration tests the InferencePool integration with AI Gateway.
// This test verifies that:
// 1. InferencePool resources can be referenced in AIGatewayRoute
// 2. Extension server properly configures ORIGINAL_DST clusters for InferencePool backends
// 3. EnvoyExtensionPolicy is created for EPP services
// Note: InferencePool environment (CRDs, vLLM, InferenceModel, InferencePool) is set up in TestMain
func TestInferencePoolIntegration(t *testing.T) {
	// Apply the simplified test manifest
	const manifest = "../../examples/inference-pool/base.yaml"
	require.NoError(t, kubectlApplyManifest(t.Context(), manifest))

	// Clean up after test
	t.Cleanup(func() {
		_ = kubectlDeleteManifest(context.Background(), manifest)
	})

	const egSelector = "gateway.envoyproxy.io/owning-gateway-name=inference-pool"
	requireWaitForGatewayPodReady(t, egSelector)
}
