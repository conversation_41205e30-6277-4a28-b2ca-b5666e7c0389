# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
gateway:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
provider:
  type: Custom
  custom:
    resource:
      type: File
      file:
        paths: ["PLACEHOLDER_TMPDIR"]
    infrastructure:
      type: Host
      host: {}
logging:
  level:
    default: error
extensionApis:
  enableBackend: true
extensionManager:
  backendResources:
    - group: inference.networking.x-k8s.io
      kind: InferencePool
      version: v1alpha2
  hooks:
    xdsTranslator:
      post:
        - VirtualHost
        - Translation
        - Cluster
  service:
    fqdn:
      hostname: localhost
      port: 1061
