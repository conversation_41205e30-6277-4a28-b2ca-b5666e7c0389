#!/bin/bash

# Quick AI Gateway Development Deployment Script
# A simplified version for rapid development iterations

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
TAG="${TAG:-latest}"
KIND_CLUSTER_NAME="${KIND_CLUSTER_NAME:-envoy-ai-gateway}"

echo -e "${BLUE}🚀 Quick AI Gateway Deployment (TAG=${TAG})${NC}"

# Check if kind cluster exists, create if not
if ! kind get clusters | grep -q "^${KIND_CLUSTER_NAME}$"; then
    echo -e "${YELLOW}⚠ Kind cluster '${KIND_CLUSTER_NAME}' not found${NC}"
    echo -e "${BLUE}🔧 Creating cluster using e2e test setup...${NC}"

    if make test-e2e GO_TEST_ARGS="-run TestInferencePoolIntegration -v" EG_VERSION=v0.0.0-latest TEST_KEEP_CLUSTER=true; then
        echo -e "${GREEN}✓ Kind cluster '${KIND_CLUSTER_NAME}' created successfully${NC}"
    else
        echo -e "${RED}✗ Failed to create kind cluster via e2e test${NC}"
        exit 1
    fi
fi

# Step 1: Build and load images
echo -e "${BLUE}📦 Building and loading images...${NC}"
make docker-build TAG="${TAG}" && \
kind load docker-image docker.io/envoyproxy/ai-gateway-extproc:${TAG} --name=${KIND_CLUSTER_NAME} && \
kind load docker-image docker.io/envoyproxy/ai-gateway-controller:${TAG} --name=${KIND_CLUSTER_NAME}

# Step 2: Update and restart deployments
echo -e "${BLUE}🔄 Updating and restarting deployments...${NC}"

# Update AI Gateway Controller image and restart
if kubectl get deployment ai-gateway-controller -n envoy-ai-gateway-system >/dev/null 2>&1; then
    echo -e "${BLUE}📝 Setting AI Gateway Controller image to ${TAG}...${NC}"
    kubectl set image deployment/ai-gateway-controller ai-gateway-helm=docker.io/envoyproxy/ai-gateway-controller:${TAG} -n envoy-ai-gateway-system
    kubectl rollout restart deployment/ai-gateway-controller -n envoy-ai-gateway-system
    echo -e "${GREEN}✓ AI Gateway Controller updated and restarted${NC}"
else
    echo -e "${YELLOW}⚠ ai-gateway-controller not found${NC}"
fi

# Restart Envoy Gateway
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system 2>/dev/null || echo -e "${YELLOW}⚠ envoy-gateway not found${NC}"

# Step 3: Restart proxy deployments
echo -e "${BLUE}🔄 Restarting proxy deployments...${NC}"
PROXY_DEPLOYMENTS=$(kubectl get deployment -l app.kubernetes.io/component=proxy -n envoy-gateway-system -o name 2>/dev/null || true)
if [[ -n "${PROXY_DEPLOYMENTS}" ]]; then
    echo "${PROXY_DEPLOYMENTS}" | xargs -I {} kubectl rollout restart {} -n envoy-gateway-system
    echo -e "${GREEN}✓ Proxy deployments restarted${NC}"
else
    echo -e "${YELLOW}⚠ No proxy deployments found${NC}"
fi

# Step 4: Show status
echo -e "${BLUE}📊 Deployment Status:${NC}"
echo "AI Gateway Controller:"
kubectl get deployment ai-gateway-controller -n envoy-ai-gateway-system 2>/dev/null || echo "  Not found"
kubectl get pods -l app.kubernetes.io/name=ai-gateway-controller -n envoy-ai-gateway-system 2>/dev/null || echo "  No pods found"

echo
echo "Envoy Gateway:"
kubectl get deployment envoy-gateway -n envoy-gateway-system 2>/dev/null || echo "  Not found"
kubectl get pods -l control-plane=envoy-gateway -n envoy-gateway-system 2>/dev/null || echo "  No pods found"

echo
echo "Proxy Deployments:"
kubectl get deployment -l app.kubernetes.io/component=proxy -n envoy-gateway-system 2>/dev/null || echo "  None found"
kubectl get pods -l app.kubernetes.io/component=proxy -n envoy-gateway-system 2>/dev/null || echo "  No proxy pods found"

echo -e "${GREEN}✅ Quick deployment completed!${NC}"
