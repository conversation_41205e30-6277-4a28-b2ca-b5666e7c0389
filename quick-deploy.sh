#!/bin/bash

# Quick AI Gateway Development Deployment Script
# A simplified version for rapid development iterations

set -euo pipefail

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
TAG="${TAG:-latest}"
KIND_CLUSTER_NAME="${KIND_CLUSTER_NAME:-envoy-ai-gateway}"

echo -e "${BLUE}🚀 Quick AI Gateway Deployment (TAG=${TAG})${NC}"

# Step 1: Build and load images
echo -e "${BLUE}📦 Building and loading images...${NC}"
make docker-build TAG="${TAG}" && \
kind load docker-image docker.io/envoyproxy/ai-gateway-extproc:${TAG} --name=${KIND_CLUSTER_NAME} && \
kind load docker-image docker.io/envoyproxy/ai-gateway-controller:${TAG} --name=${KIND_CLUSTER_NAME}

# Step 2: Restart deployments
echo -e "${BLUE}🔄 Restarting deployments...${NC}"
kubectl rollout restart deployment/ai-gateway-controller -n envoy-ai-gateway-system 2>/dev/null || echo -e "${YELLOW}⚠ ai-gateway-controller not found${NC}"
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system 2>/dev/null || echo -e "${YELLOW}⚠ envoy-gateway not found${NC}"

# Step 3: Restart proxy deployments
echo -e "${BLUE}🔄 Restarting proxy deployments...${NC}"
PROXY_DEPLOYMENTS=$(kubectl get deployment -l app.kubernetes.io/component=proxy -n envoy-gateway-system -o name 2>/dev/null || true)
if [[ -n "${PROXY_DEPLOYMENTS}" ]]; then
    echo "${PROXY_DEPLOYMENTS}" | xargs -I {} kubectl rollout restart {} -n envoy-gateway-system
    echo -e "${GREEN}✓ Proxy deployments restarted${NC}"
else
    echo -e "${YELLOW}⚠ No proxy deployments found${NC}"
fi

# Step 4: Show status
echo -e "${BLUE}📊 Deployment Status:${NC}"
echo "AI Gateway Controller:"
kubectl get deployment ai-gateway-controller -n envoy-ai-gateway-system 2>/dev/null || echo "  Not found"
echo "Envoy Gateway:"
kubectl get deployment envoy-gateway -n envoy-gateway-system 2>/dev/null || echo "  Not found"
echo "Proxy Deployments:"
kubectl get deployment -l app.kubernetes.io/component=proxy -n envoy-gateway-system 2>/dev/null || echo "  None found"

echo -e "${GREEN}✅ Quick deployment completed!${NC}"
