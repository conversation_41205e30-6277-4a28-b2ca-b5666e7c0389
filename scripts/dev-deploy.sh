#!/bin/bash

# AI Gateway Development Deployment Script
# This script automates the common development workflow:
# 1. Build Docker images
# 2. Load images into kind cluster
# 3. Restart deployments
# 4. Show deployment status

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TAG="${TAG:-latest}"
KIND_CLUSTER_NAME="${KIND_CLUSTER_NAME:-envoy-ai-gateway}"
NAMESPACE_CONTROLLER="${NAMESPACE_CONTROLLER:-envoy-ai-gateway-system}"
NAMESPACE_GATEWAY="${NAMESPACE_GATEWAY:-envoy-gateway-system}"

# Images
EXTPROC_IMAGE="docker.io/envoyproxy/ai-gateway-extproc:${TAG}"
CONTROLLER_IMAGE="docker.io/envoyproxy/ai-gateway-controller:${TAG}"

# Function to print colored output
print_step() {
    echo -e "${BLUE}==>${NC} ${1}"
}

print_success() {
    echo -e "${GREEN}✓${NC} ${1}"
}

print_error() {
    echo -e "${RED}✗${NC} ${1}"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} ${1}"
}

print_info() {
    echo -e "${CYAN}ℹ${NC} ${1}"
}

# Function to check if command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        print_error "Command '$1' not found. Please install it first."
        exit 1
    fi
}

# Function to check if kind cluster exists
check_kind_cluster() {
    if ! kind get clusters | grep -q "^${KIND_CLUSTER_NAME}$"; then
        print_error "Kind cluster '${KIND_CLUSTER_NAME}' not found."
        print_info "Please create the cluster first or set KIND_CLUSTER_NAME environment variable."
        exit 1
    fi
}

# Function to build docker images
build_images() {
    print_step "Building Docker images with TAG=${TAG}..."
    
    if make docker-build TAG="${TAG}"; then
        print_success "Docker images built successfully"
    else
        print_error "Failed to build Docker images"
        exit 1
    fi
}

# Function to load images into kind cluster
load_images() {
    print_step "Loading images into kind cluster '${KIND_CLUSTER_NAME}'..."
    
    local images=("${EXTPROC_IMAGE}" "${CONTROLLER_IMAGE}")
    
    for image in "${images[@]}"; do
        print_info "Loading ${image}..."
        if kind load docker-image "${image}" --name="${KIND_CLUSTER_NAME}"; then
            print_success "Loaded ${image}"
        else
            print_error "Failed to load ${image}"
            exit 1
        fi
    done
}

# Function to restart deployments
restart_deployments() {
    print_step "Restarting deployments..."
    
    # Restart AI Gateway controller
    print_info "Restarting ai-gateway-controller in ${NAMESPACE_CONTROLLER}..."
    if kubectl rollout restart deployment/ai-gateway-controller -n "${NAMESPACE_CONTROLLER}"; then
        print_success "Restarted ai-gateway-controller"
    else
        print_warning "Failed to restart ai-gateway-controller (might not exist yet)"
    fi
    
    # Restart Envoy Gateway
    print_info "Restarting envoy-gateway in ${NAMESPACE_GATEWAY}..."
    if kubectl rollout restart deployment/envoy-gateway -n "${NAMESPACE_GATEWAY}"; then
        print_success "Restarted envoy-gateway"
    else
        print_warning "Failed to restart envoy-gateway (might not exist yet)"
    fi
    
    # Restart proxy deployments
    print_info "Restarting proxy deployments in ${NAMESPACE_GATEWAY}..."
    local proxy_deployments
    proxy_deployments=$(kubectl get deployment -l app.kubernetes.io/component=proxy -n "${NAMESPACE_GATEWAY}" -o name 2>/dev/null || true)
    
    if [[ -n "${proxy_deployments}" ]]; then
        echo "${proxy_deployments}" | while read -r deployment; do
            deployment_name=$(echo "${deployment}" | cut -d'/' -f2)
            print_info "Restarting ${deployment_name}..."
            if kubectl rollout restart "${deployment}" -n "${NAMESPACE_GATEWAY}"; then
                print_success "Restarted ${deployment_name}"
            else
                print_warning "Failed to restart ${deployment_name}"
            fi
        done
    else
        print_info "No proxy deployments found to restart"
    fi
}

# Function to show deployment status
show_status() {
    print_step "Checking deployment status..."
    
    echo -e "\n${PURPLE}AI Gateway Controller Status:${NC}"
    kubectl get deployment ai-gateway-controller -n "${NAMESPACE_CONTROLLER}" 2>/dev/null || print_info "ai-gateway-controller not found"
    
    echo -e "\n${PURPLE}Envoy Gateway Status:${NC}"
    kubectl get deployment envoy-gateway -n "${NAMESPACE_GATEWAY}" 2>/dev/null || print_info "envoy-gateway not found"
    
    echo -e "\n${PURPLE}Proxy Deployments Status:${NC}"
    kubectl get deployment -l app.kubernetes.io/component=proxy -n "${NAMESPACE_GATEWAY}" 2>/dev/null || print_info "No proxy deployments found"
}

# Function to wait for deployments to be ready
wait_for_deployments() {
    print_step "Waiting for deployments to be ready..."
    
    # Wait for AI Gateway controller
    print_info "Waiting for ai-gateway-controller..."
    if kubectl rollout status deployment/ai-gateway-controller -n "${NAMESPACE_CONTROLLER}" --timeout=300s 2>/dev/null; then
        print_success "ai-gateway-controller is ready"
    else
        print_warning "ai-gateway-controller rollout status check failed or timed out"
    fi
    
    # Wait for Envoy Gateway
    print_info "Waiting for envoy-gateway..."
    if kubectl rollout status deployment/envoy-gateway -n "${NAMESPACE_GATEWAY}" --timeout=300s 2>/dev/null; then
        print_success "envoy-gateway is ready"
    else
        print_warning "envoy-gateway rollout status check failed or timed out"
    fi
    
    # Wait for proxy deployments
    local proxy_deployments
    proxy_deployments=$(kubectl get deployment -l app.kubernetes.io/component=proxy -n "${NAMESPACE_GATEWAY}" -o name 2>/dev/null || true)
    
    if [[ -n "${proxy_deployments}" ]]; then
        echo "${proxy_deployments}" | while read -r deployment; do
            deployment_name=$(echo "${deployment}" | cut -d'/' -f2)
            print_info "Waiting for ${deployment_name}..."
            if kubectl rollout status "${deployment}" -n "${NAMESPACE_GATEWAY}" --timeout=300s 2>/dev/null; then
                print_success "${deployment_name} is ready"
            else
                print_warning "${deployment_name} rollout status check failed or timed out"
            fi
        done
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
AI Gateway Development Deployment Script

Usage: $0 [OPTIONS]

Options:
    -h, --help          Show this help message
    -t, --tag TAG       Set the Docker image tag (default: latest)
    -c, --cluster NAME  Set the kind cluster name (default: envoy-ai-gateway)
    -s, --skip-build    Skip the Docker build step
    -w, --wait          Wait for deployments to be ready
    --no-restart        Skip restarting deployments
    --status-only       Only show deployment status

Environment Variables:
    TAG                 Docker image tag (default: latest)
    KIND_CLUSTER_NAME   Kind cluster name (default: envoy-ai-gateway)
    NAMESPACE_CONTROLLER Controller namespace (default: envoy-ai-gateway-system)
    NAMESPACE_GATEWAY   Gateway namespace (default: envoy-gateway-system)

Examples:
    $0                          # Full deployment with default settings
    $0 -t v1.0.0               # Deploy with specific tag
    $0 --skip-build            # Skip build, only load and restart
    $0 --status-only           # Only show current status
    $0 -w                      # Wait for deployments to be ready

EOF
}

# Parse command line arguments
SKIP_BUILD=false
WAIT_FOR_READY=false
NO_RESTART=false
STATUS_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -c|--cluster)
            KIND_CLUSTER_NAME="$2"
            shift 2
            ;;
        -s|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -w|--wait)
            WAIT_FOR_READY=true
            shift
            ;;
        --no-restart)
            NO_RESTART=true
            shift
            ;;
        --status-only)
            STATUS_ONLY=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo -e "${CYAN}"
    cat << 'EOF'
    ___    ____   ______      __                           
   /   |  /  _/  / ____/___ _/ /____  _      ______ ___  __
  / /| |  / /   / / __/ __ `/ __/ _ \| | /| / / __ `/ / / /
 / ___ |_/ /   / /_/ / /_/ / /_/  __/| |/ |/ / /_/ / /_/ / 
/_/  |_/___/   \____/\__,_/\__/\___/ |__/|__/\__,_/\__, /  
                                                  /____/   
Development Deployment Script
EOF
    echo -e "${NC}"
    
    print_info "Configuration:"
    print_info "  TAG: ${TAG}"
    print_info "  KIND_CLUSTER_NAME: ${KIND_CLUSTER_NAME}"
    print_info "  NAMESPACE_CONTROLLER: ${NAMESPACE_CONTROLLER}"
    print_info "  NAMESPACE_GATEWAY: ${NAMESPACE_GATEWAY}"
    echo
    
    # Check prerequisites
    check_command "make"
    check_command "docker"
    check_command "kind"
    check_command "kubectl"
    check_kind_cluster
    
    if [[ "${STATUS_ONLY}" == "true" ]]; then
        show_status
        exit 0
    fi
    
    # Execute steps
    if [[ "${SKIP_BUILD}" == "false" ]]; then
        build_images
    else
        print_info "Skipping Docker build step"
    fi
    
    load_images
    
    if [[ "${NO_RESTART}" == "false" ]]; then
        restart_deployments
    else
        print_info "Skipping deployment restart step"
    fi
    
    if [[ "${WAIT_FOR_READY}" == "true" ]]; then
        wait_for_deployments
    fi
    
    show_status
    
    print_success "Development deployment completed successfully!"
    print_info "You can now test your changes."
}

# Run main function
main "$@"
