# AI Gateway Development Scripts

This directory contains scripts to streamline the AI Gateway development workflow.

## Scripts Overview

### 1. `dev-deploy.sh` - Comprehensive Development Deployment

A full-featured script with extensive options and error handling.

**Features:**
- ✅ Colored output with progress indicators
- ✅ Comprehensive error handling
- ✅ Flexible configuration via environment variables and CLI options
- ✅ Status checking and deployment waiting
- ✅ Detailed logging and feedback

**Usage:**
```bash
# Basic usage
./scripts/dev-deploy.sh

# With custom tag
./scripts/dev-deploy.sh -t v1.0.0

# Skip build step (useful when images already exist)
./scripts/dev-deploy.sh --skip-build

# Wait for deployments to be ready
./scripts/dev-deploy.sh -w

# Only show current status
./scripts/dev-deploy.sh --status-only

# Full help
./scripts/dev-deploy.sh --help
```

### 2. `quick-deploy.sh` - Rapid Development Deployment

A streamlined script for quick iterations during development.

**Features:**
- ⚡ Fast execution with minimal overhead
- 🎯 Focused on essential deployment steps
- 📊 Basic status reporting
- 🔧 Simple configuration

**Usage:**
```bash
# Quick deployment with default settings
./scripts/quick-deploy.sh

# With custom tag
TAG=v1.0.0 ./scripts/quick-deploy.sh

# With custom kind cluster
KIND_CLUSTER_NAME=my-cluster ./scripts/quick-deploy.sh
```

## Makefile Integration

Both scripts are integrated into the Makefile for convenience:

```bash
# Run comprehensive deployment
make dev-deploy

# Run quick deployment
make quick-deploy
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `TAG` | `latest` | Docker image tag |
| `KIND_CLUSTER_NAME` | `envoy-ai-gateway` | Kind cluster name |
| `NAMESPACE_CONTROLLER` | `envoy-ai-gateway-system` | Controller namespace |
| `NAMESPACE_GATEWAY` | `envoy-gateway-system` | Gateway namespace |

### Examples

```bash
# Deploy with specific tag
TAG=v1.2.3 make quick-deploy

# Deploy to different cluster
KIND_CLUSTER_NAME=my-test-cluster ./scripts/dev-deploy.sh

# Deploy with all custom settings
TAG=dev-branch \
KIND_CLUSTER_NAME=test-cluster \
NAMESPACE_CONTROLLER=custom-controller \
NAMESPACE_GATEWAY=custom-gateway \
./scripts/dev-deploy.sh
```

## Workflow Steps

Both scripts perform the following core steps:

1. **Build Docker Images** 📦
   - Runs `make docker-build TAG=${TAG}`
   - Builds both controller and extproc images

2. **Load Images into Kind** 🚀
   - Loads `docker.io/envoyproxy/ai-gateway-extproc:${TAG}`
   - Loads `docker.io/envoyproxy/ai-gateway-controller:${TAG}`

3. **Restart Deployments** 🔄
   - Restarts `ai-gateway-controller` in controller namespace
   - Restarts `envoy-gateway` in gateway namespace
   - Restarts all proxy deployments with label `app.kubernetes.io/component=proxy`

4. **Show Status** 📊
   - Displays current deployment status
   - Shows pod readiness information

## Prerequisites

Ensure you have the following tools installed:

- `make` - For building images
- `docker` - For container operations
- `kind` - For local Kubernetes cluster
- `kubectl` - For Kubernetes operations

## Troubleshooting

### Common Issues

1. **Kind cluster not found**
   ```bash
   # Check existing clusters
   kind get clusters
   
   # Create cluster if needed
   kind create cluster --name envoy-ai-gateway
   ```

2. **Images not loading**
   ```bash
   # Check if images exist locally
   docker images | grep ai-gateway
   
   # Rebuild if necessary
   make docker-build TAG=latest
   ```

3. **Deployments not found**
   ```bash
   # Check if namespaces exist
   kubectl get namespaces
   
   # Check deployments in namespace
   kubectl get deployments -n envoy-ai-gateway-system
   kubectl get deployments -n envoy-gateway-system
   ```

4. **Permission issues**
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh
   ```

### Debug Mode

For debugging, you can run commands manually:

```bash
# Build images
make docker-build TAG=latest

# Load images
kind load docker-image docker.io/envoyproxy/ai-gateway-extproc:latest --name=envoy-ai-gateway
kind load docker-image docker.io/envoyproxy/ai-gateway-controller:latest --name=envoy-ai-gateway

# Restart deployments
kubectl rollout restart deployment/ai-gateway-controller -n envoy-ai-gateway-system
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system

# Check proxy deployments
kubectl get deployment -l app.kubernetes.io/component=proxy -n envoy-gateway-system
```

## Tips for Development

1. **Use quick-deploy for rapid iteration** - When making frequent changes, use `make quick-deploy` for faster feedback loops.

2. **Use dev-deploy for comprehensive testing** - When you need to ensure everything is working correctly, use `make dev-deploy -w` to wait for deployments.

3. **Check status regularly** - Use `./scripts/dev-deploy.sh --status-only` to quickly check deployment health.

4. **Customize for your workflow** - Set environment variables in your shell profile for consistent configuration.

## Integration with IDE

You can integrate these scripts with your IDE:

### VS Code Tasks

Add to `.vscode/tasks.json`:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "AI Gateway: Quick Deploy",
            "type": "shell",
            "command": "make quick-deploy",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "AI Gateway: Full Deploy",
            "type": "shell",
            "command": "make dev-deploy",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        }
    ]
}
```
