// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extensionserver

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	egextension "github.com/envoyproxy/gateway/proto/extension"
	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	endpointv3 "github.com/envoyproxy/go-control-plane/envoy/config/endpoint/v3"
	extprocv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/http/ext_proc/v3"
	httpconnectionmanagerv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/network/http_connection_manager/v3"
	tlsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"
	upstreamsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/upstreams/http/v3"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/structpb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/utils/ptr"
	gwaiev1a2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"

	"github.com/envoyproxy/ai-gateway/internal/internalapi"
)

// PostClusterModify allows an extension to modify clusters after they are generated.
func (s *Server) PostClusterModify(_ context.Context, req *egextension.PostClusterModifyRequest) (*egextension.PostClusterModifyResponse, error) {
	s.log.Info("Called PostClusterModify", "cluster", req.Cluster.Name)
	// Check if we have backend extension resources (InferencePool resources).
	if req.PostClusterContext == nil || len(req.PostClusterContext.BackendExtensionResources) == 0 {
		// No backend extension resources, skip.
		return &egextension.PostClusterModifyResponse{Cluster: req.Cluster}, nil
	}

	// Parse InferencePool resources from BackendExtensionResources.
	var inferencePool *gwaiev1a2.InferencePool
	for _, resource := range req.PostClusterContext.BackendExtensionResources {
		// Unmarshal the unstructured bytes to get the resource.
		var unstructuredObj unstructured.Unstructured
		if err := json.Unmarshal(resource.UnstructuredBytes, &unstructuredObj); err != nil {
			s.log.Error(err, "failed to unmarshal extension resource")
			continue
		}

		// Check if this is an InferencePool resource.
		if unstructuredObj.GetAPIVersion() == "inference.networking.x-k8s.io/v1alpha2" &&
			unstructuredObj.GetKind() == "InferencePool" {
			// Convert unstructured to InferencePool.
			var pool gwaiev1a2.InferencePool
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(unstructuredObj.Object, &pool); err != nil {
				s.log.Error(err, "failed to convert unstructured to InferencePool",
					"name", unstructuredObj.GetName(), "namespace", unstructuredObj.GetNamespace())
				continue
			}
			inferencePool = &pool
			break // We only support one InferencePool per cluster based on CEL validation.
		}
	}

	// If we found an InferencePool, configure the cluster for ORIGINAL_DST.
	if inferencePool != nil {
		s.handleInferencePoolCluster(req.Cluster, inferencePool)
	}

	return &egextension.PostClusterModifyResponse{Cluster: req.Cluster}, nil
}

// handleInferencePoolCluster modifies clusters that have InferencePool backends using the provided resource.
func (s *Server) handleInferencePoolCluster(cluster *clusterv3.Cluster, inferencePool *gwaiev1a2.InferencePool) {
	s.log.Info("Handling InferencePool cluster with resource", "cluster_name", cluster.Name, "inference_pool", inferencePool.Name)

	// Configure cluster for ORIGINAL_DST with header-based load balancing.
	cluster.ClusterDiscoveryType = &clusterv3.Cluster_Type{Type: clusterv3.Cluster_ORIGINAL_DST}
	cluster.LbPolicy = clusterv3.Cluster_CLUSTER_PROVIDED
	cluster.ConnectTimeout = durationpb.New(1000 * time.Second)

	// Configure original destination load balancer to use HTTP header.
	cluster.LbConfig = &clusterv3.Cluster_OriginalDstLbConfig_{
		OriginalDstLbConfig: &clusterv3.Cluster_OriginalDstLbConfig{
			UseHttpHeader:  true,
			HttpHeaderName: "x-gateway-destination-endpoint",
		},
	}
	cluster.LoadBalancingPolicy = nil
	// Remove EDS config since we are using ORIGINAL_DST.
	cluster.EdsClusterConfig = nil

	// Add InferencePool metadata to the cluster.
	buildMetadataForInferencePool(cluster, inferencePool)

	s.log.Info("Configured cluster for InferencePool with ORIGINAL_DST",
		"cluster_name", cluster.Name,
		"inference_pool", inferencePool.Name,
		"namespace", inferencePool.Namespace)
}

// buildMetadataForInferencePool adds InferencePool metadata to the cluster.
func buildMetadataForInferencePool(cluster *clusterv3.Cluster, inferencePool *gwaiev1a2.InferencePool) {
	// Add InferencePool metadata to the cluster.
	if cluster.Metadata == nil {
		cluster.Metadata = &corev3.Metadata{}
	}
	if cluster.Metadata.FilterMetadata == nil {
		cluster.Metadata.FilterMetadata = make(map[string]*structpb.Struct)
	}
	m, ok := cluster.Metadata.FilterMetadata[internalapi.InternalEndpointMetadataNamespace]
	if !ok {
		m = &structpb.Struct{}
		cluster.Metadata.FilterMetadata[internalapi.InternalEndpointMetadataNamespace] = m
	}
	if m.Fields == nil {
		m.Fields = make(map[string]*structpb.Value)
	}
	m.Fields[internalapi.InternalMetadataInferencePoolKey] = structpb.NewStringValue(
		internalapi.ClusterRefInferencePool(inferencePool.Namespace, inferencePool.Name, string(inferencePool.Spec.EndpointPickerConfig.ExtensionRef.Name), portForInferencePool(inferencePool)),
	)
}

// getInferencePoolByMetadata returns the InferencePool from the cluster metadata.
func getInferencePoolByMetadata(cluster *clusterv3.Cluster) *gwaiev1a2.InferencePool {
	var metadata string
	if cluster.Metadata != nil && cluster.Metadata.FilterMetadata != nil {
		m, ok := cluster.Metadata.FilterMetadata[internalapi.InternalEndpointMetadataNamespace]
		if ok && m.Fields != nil {
			v, ok := m.Fields[internalapi.InternalMetadataInferencePoolKey]
			if ok {
				metadata = v.GetStringValue()
			}
		}
	}

	result := strings.Split(metadata, "/")
	if len(result) != 4 {
		return nil
	}
	ns := result[0]
	name := result[1]
	serviceName := result[2]
	port, err := strconv.ParseInt(result[3], 10, 32)
	if err != nil {
		return nil
	}
	return &gwaiev1a2.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: ns,
		},
		Spec: gwaiev1a2.InferencePoolSpec{
			EndpointPickerConfig: gwaiev1a2.EndpointPickerConfig{
				ExtensionRef: &gwaiev1a2.Extension{
					ExtensionReference: gwaiev1a2.ExtensionReference{
						Name:       gwaiev1a2.ObjectName(serviceName),
						PortNumber: ptr.To(gwaiev1a2.PortNumber(port)),
					},
				},
			},
		},
	}
}

// buildClustersForInferencePool builds and returns a "STRICT_DNS" cluster for each InferencePool cluster.
func buildClustersForInferencePool(clusters []*clusterv3.Cluster) []*clusterv3.Cluster {
	result := make([]*clusterv3.Cluster, 0, len(clusters))
	for _, cluster := range clusters {
		if pool := getInferencePoolByMetadata(cluster); pool != nil {
			result = append(result, buildExtProcClusterForInferencePool(pool))
		}
	}

	return result
}

// buildExtProcClusterForInferencePool builds and returns a "STRICT_DNS" cluster from the given pool.
func buildExtProcClusterForInferencePool(pool *gwaiev1a2.InferencePool) *clusterv3.Cluster {
	if pool == nil || pool.Spec.EndpointPickerConfig.ExtensionRef == nil {
		return nil
	}

	name := clusterNameExtProcForInferencePool(pool.GetName(), pool.GetNamespace())
	c := &clusterv3.Cluster{
		Name:           name,
		ConnectTimeout: durationpb.New(10 * time.Second),
		ClusterDiscoveryType: &clusterv3.Cluster_Type{
			Type: clusterv3.Cluster_STRICT_DNS,
		},
		LbPolicy: clusterv3.Cluster_LEAST_REQUEST,
		// Ensure Envoy accepts untrusted certificates.
		TransportSocket: &corev3.TransportSocket{
			Name: "envoy.transport_sockets.tls",
			ConfigType: &corev3.TransportSocket_TypedConfig{
				TypedConfig: func() *anypb.Any {
					tlsCtx := &tlsv3.UpstreamTlsContext{
						CommonTlsContext: &tlsv3.CommonTlsContext{
							ValidationContextType: &tlsv3.CommonTlsContext_ValidationContext{},
						},
					}
					anyTLS := mustToAny(tlsCtx)
					return anyTLS
				}(),
			},
		},
		LoadAssignment: &endpointv3.ClusterLoadAssignment{
			ClusterName: name,
			Endpoints: []*endpointv3.LocalityLbEndpoints{{
				LbEndpoints: []*endpointv3.LbEndpoint{{
					HealthStatus: corev3.HealthStatus_HEALTHY,
					HostIdentifier: &endpointv3.LbEndpoint_Endpoint{
						Endpoint: &endpointv3.Endpoint{
							Address: &corev3.Address{
								Address: &corev3.Address_SocketAddress{
									SocketAddress: &corev3.SocketAddress{
										Address:  dnsNameForInferencePool(pool),
										Protocol: corev3.SocketAddress_TCP,
										PortSpecifier: &corev3.SocketAddress_PortValue{
											PortValue: portForInferencePool(pool),
										},
									},
								},
							},
						},
					},
				}},
			}},
		},
	}

	http2Opts := &upstreamsv3.HttpProtocolOptions{
		UpstreamProtocolOptions: &upstreamsv3.HttpProtocolOptions_ExplicitHttpConfig_{
			ExplicitHttpConfig: &upstreamsv3.HttpProtocolOptions_ExplicitHttpConfig{
				ProtocolConfig: &upstreamsv3.HttpProtocolOptions_ExplicitHttpConfig_Http2ProtocolOptions{
					Http2ProtocolOptions: &corev3.Http2ProtocolOptions{},
				},
			},
		},
	}

	anyHTTP2 := mustToAny(http2Opts)
	c.TypedExtensionProtocolOptions = map[string]*anypb.Any{
		"envoy.extensions.upstreams.http.v3.HttpProtocolOptions": anyHTTP2,
	}

	return c
}

// dummyHTTPFilterForInferencePool returns a dummy HTTP filter for InferencePool.
func dummyHTTPFilterForInferencePool() *httpconnectionmanagerv3.HttpFilter {
	pool := &gwaiev1a2.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dummy",
			Namespace: "dummy",
		},
		Spec: gwaiev1a2.InferencePoolSpec{
			EndpointPickerConfig: gwaiev1a2.EndpointPickerConfig{
				ExtensionRef: &gwaiev1a2.Extension{
					ExtensionReference: gwaiev1a2.ExtensionReference{
						Name: "dummy",
					},
				},
			},
		},
	}

	poolFilter := buildHTTPFilterForInferencePool(pool)
	return &httpconnectionmanagerv3.HttpFilter{
		Name:       ExtProcNameInferencePool,
		ConfigType: &httpconnectionmanagerv3.HttpFilter_TypedConfig{TypedConfig: mustToAny(poolFilter)},
		Disabled:   true,
	}
}

// buildHTTPFilterForInferencePool returns the HTTP filter for the given InferencePool.
func buildHTTPFilterForInferencePool(pool *gwaiev1a2.InferencePool) *extprocv3.ExternalProcessor {
	return &extprocv3.ExternalProcessor{
		GrpcService: &corev3.GrpcService{
			TargetSpecifier: &corev3.GrpcService_EnvoyGrpc_{
				EnvoyGrpc: &corev3.GrpcService_EnvoyGrpc{
					ClusterName: clusterNameExtProcForInferencePool(
						pool.GetName(),
						pool.GetNamespace(),
					),
					Authority: authorityForInferencePool(pool),
				},
			},
		},
		ProcessingMode: &extprocv3.ProcessingMode{
			RequestHeaderMode:   extprocv3.ProcessingMode_SEND,
			RequestBodyMode:     extprocv3.ProcessingMode_FULL_DUPLEX_STREAMED,
			RequestTrailerMode:  extprocv3.ProcessingMode_SEND,
			ResponseBodyMode:    extprocv3.ProcessingMode_FULL_DUPLEX_STREAMED,
			ResponseHeaderMode:  extprocv3.ProcessingMode_SEND,
			ResponseTrailerMode: extprocv3.ProcessingMode_SEND,
		},
		MessageTimeout:   durationpb.New(5 * time.Second),
		FailureModeAllow: false,
	}
}

// authorityForInferencePool formats the gRPC authority based on the given InferencePool.
func authorityForInferencePool(pool *gwaiev1a2.InferencePool) string {
	ns := pool.GetNamespace()
	svc := pool.Spec.EndpointPickerConfig.ExtensionRef.Name
	return fmt.Sprintf("%s.%s.svc:%d", svc, ns, portForInferencePool(pool))
}

// dnsNameForInferencePool formats the DNS name based on the given InferencePool.
func dnsNameForInferencePool(pool *gwaiev1a2.InferencePool) string {
	ns := pool.GetNamespace()
	svc := pool.Spec.EndpointPickerConfig.ExtensionRef.Name
	return fmt.Sprintf("%s.%s.svc", svc, ns)
}

// portForInferencePool returns the port number for the given InferencePool.
func portForInferencePool(pool *gwaiev1a2.InferencePool) uint32 {
	if p := pool.Spec.ExtensionRef.ExtensionReference.PortNumber; p == nil {
		return uint32(9002)
	}
	portNumber := *pool.Spec.ExtensionRef.ExtensionReference.PortNumber
	if portNumber < 0 || portNumber > 65535 {
		return uint32(9002) // fallback to default port.
	}
	// Safe conversion: portNumber is validated to be in range [0, 65535].
	return uint32(portNumber) // #nosec G115
}

// clusterNameExtProcForInferencePool returns the name of the ext_proc cluster for the given InferencePool.
func clusterNameExtProcForInferencePool(name, ns string) string {
	return fmt.Sprintf("endpointpicker_%s_%s_ext_proc", name, ns)
}
